# Translations template for WTForms.
# Copyright (C) 2024 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2024.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: WTForms 3.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-01-11 08:20+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr ""

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr ""

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr ""

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr ""

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr ""

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr ""

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr ""

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr ""

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr ""

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr ""

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr ""

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr ""

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr ""

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr ""

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr ""

#: src/wtforms/validators.py:698
msgid "This field cannot be edited."
msgstr ""

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value."
msgstr ""

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr ""

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr ""

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr ""

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr ""

#: src/wtforms/fields/choices.py:142
msgid "Invalid Choice: could not coerce."
msgstr ""

#: src/wtforms/fields/choices.py:149 src/wtforms/fields/choices.py:203
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/choices.py:155
msgid "Not a valid choice."
msgstr ""

#: src/wtforms/fields/choices.py:193
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""

#: src/wtforms/fields/choices.py:214
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr ""

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr ""

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr ""

#: src/wtforms/fields/datetime.py:148
msgid "Not a valid week value."
msgstr ""

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr ""

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr ""

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr ""
