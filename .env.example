# Flask Configuration
FLASK_ENV=deployment
FLASK_APP=app.py
SECRET_KEY=your-secret-key-here

# Database
DATABASE_URL=sqlite:///shop_menu.db

# Admin User (for initial setup)
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Google Translate API (optional)
GOOGLE_TRANSLATE_API_KEY=your-google-translate-api-key

# Upload Settings
MAX_CONTENT_LENGTH=1048576  # 1MB in bytes

# Babel Configuration
BABEL_DEFAULT_LOCALE=ar
BABEL_DEFAULT_TIMEZONE=UTC

# Production Settings (uncomment for production)
# FLASK_ENV=production
# DATABASE_URL=postgresql://user:password@localhost/dbname
# SECRET_KEY=your-production-secret-key
