# Translations template for WTForms.
# Copyright (C) 2023 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 3.0.1\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-01-11 08:20+0100\n"
"PO-Revision-Date: 2023-10-07 06:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Romanian <https://hosted.weblate.org/projects/wtforms/wtforms/"
"ro/>\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n==1 ? 0 : (n==0 || (n%100 > 0 && n%100 < "
"20)) ? 1 : 2;\n"
"X-Generator: Weblate 5.1-dev\n"
"Generated-By: Babel 2.12.1\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Numele câmpului este invalid '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Câmpul trebuie să fie egal cu %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Câmpul trebuie să aibă minim %(min)d caracter."
msgstr[1] "Câmpul trebuie să aibă minim %(min)d caractere."
msgstr[2] "Câmpul trebuie să aibă minim %(min)d de caractere."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Câmpul nu poate fi mai mare de %(max)d caracter."
msgstr[1] "Câmpul nu poate fi mai mare de %(max)d caractere."
msgstr[2] "Câmpul nu poate fi mai mare de %(max)d de caractere."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] "Câmpul trebuie să aibă exact %(max)d caracter."
msgstr[1] "Câmpul trebuie să aibă exact %(max)d caractere."
msgstr[2] "Câmpul trebuie să aibă exact %(max)d de caractere."

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Câmpul trebuie să aibă între %(min)d și %(max)d caractere."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Numărul trebuie să fie cel puțin %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Numărul trebuie să fie cel mult %(max)s."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Numărul trebuie să fie între %(min)s și %(max)s."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Acest câmp este obligatoriu."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Valoarea introdusă este invalidă."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Adresa email este invalidă."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Adresa IP este invalidă."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Adresa Mac este invalidă."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "URL invalid."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "UUID invalid."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Valoare invalidă, trebuie să fie una din: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Valoare invalidă, nu trebuie să fie una din: %(values)s."

#: src/wtforms/validators.py:698
#, fuzzy
#| msgid "This field cannot be edited"
msgid "This field cannot be edited."
msgstr "Câmpul nu poate fi editat"

#: src/wtforms/validators.py:714
#, fuzzy
#| msgid "This field is disabled and cannot have a value"
msgid "This field is disabled and cannot have a value."
msgstr "Câmpul este dezactivat și nu poate conține o valoare"

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Token-ul CSRF este invalid."

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Lipsește token-ul CSRF."

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "Validarea CSRF a eșuat."

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Token-ul CSRF a expirat."

#: src/wtforms/fields/choices.py:142
msgid "Invalid Choice: could not coerce."
msgstr "Selecție invalidă: valoarea nu a putut fi transformată."

#: src/wtforms/fields/choices.py:149 src/wtforms/fields/choices.py:203
msgid "Choices cannot be None."
msgstr "Selecția nu poate fi None."

#: src/wtforms/fields/choices.py:155
msgid "Not a valid choice."
msgstr "Selecție invalidă."

#: src/wtforms/fields/choices.py:193
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Selecție(ii) invalidă: una sau mai multe valori nu au putut fi transformate."

#: src/wtforms/fields/choices.py:214
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "'%(value)s' nu este o selecție validă pentru acest câmp."
msgstr[1] "'%(value)s' nu sunt selecții valide pentru acest câmp."
msgstr[2] "'%(value)s' nu sunt selecții valide pentru acest câmp."

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Valoare dată-timp invalidă."

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Valoarea pentru dată este invalidă."

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr "Valoarea pentru timp este invalidă."

#: src/wtforms/fields/datetime.py:148
msgid "Not a valid week value."
msgstr "Valoarea pentru săptămână este invalidă."

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Număr întreg invalid."

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Număr zecimal invalid."

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Număr cu virgulă invalid."
