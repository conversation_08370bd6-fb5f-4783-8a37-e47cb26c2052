{% extends "admin/base.html" %}

{% block page_title %}{{ title }}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
            <p class="text-gray-600">
                {% if user %}
                    {{ _('تعديل بيانات المستخدم') }}
                {% else %}
                    {{ _('إضافة مستخدم جديد للنظام') }}
                {% endif %}
            </p>
        </div>
        <a href="{{ url_for('admin.users') }}" 
           class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors">
            <i class="fas fa-arrow-{{ 'left' if current_lang != 'ar' else 'right' }} {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
            {{ _('العودة للقائمة') }}
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow">
        <form method="POST" class="p-6 space-y-6">
            {{ form.hidden_tag() }}
            
            <!-- Username -->
            <div>
                {{ form.username.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                {{ form.username(class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent") }}
                {% if form.username.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.username.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">{{ _('اسم المستخدم يجب أن يكون فريداً') }}</p>
            </div>

            <!-- Email -->
            <div>
                {{ form.email.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                {{ form.email(class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent") }}
                {% if form.email.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.email.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">{{ _('البريد الإلكتروني يجب أن يكون صحيحاً وفريداً') }}</p>
            </div>

            <!-- Password -->
            <div>
                {{ form.password.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                {{ form.password(class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent") }}
                {% if form.password.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.password.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">
                    {% if user %}
                        {{ _('اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور') }}
                    {% else %}
                        {{ _('كلمة المرور يجب أن تكون 6 أحرف على الأقل') }}
                    {% endif %}
                </p>
            </div>

            <!-- Confirm Password -->
            <div>
                {{ form.confirm_password.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                {{ form.confirm_password(class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent") }}
                {% if form.confirm_password.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.confirm_password.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">{{ _('يجب أن تطابق كلمة المرور') }}</p>
            </div>

            <!-- Is Active -->
            <div class="flex items-center">
                {{ form.is_active(class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded") }}
                {{ form.is_active.label(class="mr-2 ml-2 block text-sm text-gray-900") }}
                <p class="text-sm text-gray-500">{{ _('المستخدمون غير النشطين لا يمكنهم تسجيل الدخول') }}</p>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-3">
                <a href="{{ url_for('admin.users') }}" 
                   class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    {{ _('إلغاء') }}
                </a>
                <button type="submit" 
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    {% if user %}
                        <i class="fas fa-save {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
                        {{ _('حفظ التغييرات') }}
                    {% else %}
                        <i class="fas fa-plus {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
                        {{ _('إضافة المستخدم') }}
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('{{ _("كلمات المرور غير متطابقة") }}');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    if (password && confirmPassword) {
        password.addEventListener('change', validatePassword);
        confirmPassword.addEventListener('keyup', validatePassword);
    }
});
</script>
{% endblock %}
