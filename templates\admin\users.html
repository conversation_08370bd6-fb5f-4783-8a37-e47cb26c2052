{% extends "admin/base.html" %}

{% block page_title %}{{ _('المستخدمين') }}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ _('إدارة المستخدمين') }}</h1>
            <p class="text-gray-600">{{ _('إدارة مستخدمي النظام') }}</p>
        </div>
        <a href="{{ url_for('admin.add_user') }}" 
           class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
            <i class="fas fa-plus {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
            {{ _('إضافة مستخدم جديد') }}
        </a>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        {% if users %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-{{ 'left' if current_lang != 'ar' else 'right' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ _('المعرف') }}
                        </th>
                        <th class="px-6 py-3 text-{{ 'left' if current_lang != 'ar' else 'right' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ _('اسم المستخدم') }}
                        </th>
                        <th class="px-6 py-3 text-{{ 'left' if current_lang != 'ar' else 'right' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ _('البريد الإلكتروني') }}
                        </th>
                        <th class="px-6 py-3 text-{{ 'left' if current_lang != 'ar' else 'right' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ _('الحالة') }}
                        </th>
                        <th class="px-6 py-3 text-{{ 'left' if current_lang != 'ar' else 'right' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ _('تاريخ الإنشاء') }}
                        </th>
                        <th class="px-6 py-3 text-{{ 'left' if current_lang != 'ar' else 'right' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ _('الإجراءات') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for user in users %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ user.id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <div class="h-8 w-8 rounded-full bg-primary text-white flex items-center justify-center text-sm font-medium">
                                        {{ user.username[0].upper() }}
                                    </div>
                                </div>
                                <div class="{{ 'ml-4' if current_lang != 'ar' else 'mr-4' }}">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ user.username }}
                                        {% if user.id == current_user.id %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}">
                                            {{ _('أنت') }}
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ user.email }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if user.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle {{ 'mr-1' if current_lang != 'ar' else 'ml-1' }}"></i>
                                {{ _('نشط') }}
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-times-circle {{ 'mr-1' if current_lang != 'ar' else 'ml-1' }}"></i>
                                {{ _('غير نشط') }}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ user.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="{{ url_for('admin.edit_user', id=user.id) }}" 
                               class="text-indigo-600 hover:text-indigo-900">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% if user.id != current_user.id and users|length > 1 %}
                            <form method="POST" action="{{ url_for('admin.delete_user', id=user.id) }}"
                                  class="inline" onsubmit="return confirm('{{ _('هل أنت متأكد من حذف هذا المستخدم؟') }}')">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <button type="submit" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ _('لا يوجد مستخدمين') }}</h3>
            <p class="text-gray-500 mb-6">{{ _('ابدأ بإضافة مستخدمين جدد للنظام') }}</p>
            <a href="{{ url_for('admin.add_user') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90">
                <i class="fas fa-plus {{ 'ml-2' if current_lang != 'ar' else 'mr-2' }}"></i>
                {{ _('إضافة مستخدم جديد') }}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
